import { useState } from 'react'

function CommandsPanel() {
  const [commands, setCommands] = useState([
    { id: 1, name: 'ban', description: 'حظر عضو من الخادم', enabled: true, permission: 'ADMINISTRATOR' },
    { id: 2, name: 'kick', description: 'طرد عضو من الخادم', enabled: true, permission: 'KICK_MEMBERS' },
    { id: 3, name: 'mute', description: 'كتم عضو في الخادم', enabled: true, permission: 'MANAGE_MESSAGES' },
    { id: 4, name: 'warn', description: 'إعطاء تحذير لعضو', enabled: true, permission: 'MODERATE_MEMBERS' },
    { id: 5, name: 'clear', description: 'حذف رسائل من القناة', enabled: true, permission: 'MANAGE_MESSAGES' },
    { id: 6, name: 'lock', description: 'قفل القناة', enabled: false, permission: '<PERSON><PERSON>GE_CHANNELS' },
  ])

  const [newCommand, setNewCommand] = useState({
    name: '',
    description: '',
    permission: 'MANAGE_MESSAGES',
    enabled: true
  })

  const [showAddForm, setShowAddForm] = useState(false)

  const toggleCommand = (id) => {
    setCommands(commands.map(cmd => 
      cmd.id === id ? { ...cmd, enabled: !cmd.enabled } : cmd
    ))
  }

  const deleteCommand = (id) => {
    setCommands(commands.filter(cmd => cmd.id !== id))
  }

  const addCommand = () => {
    if (newCommand.name && newCommand.description) {
      const command = {
        id: Date.now(),
        ...newCommand
      }
      setCommands([...commands, command])
      setNewCommand({ name: '', description: '', permission: 'MANAGE_MESSAGES', enabled: true })
      setShowAddForm(false)
    }
  }

  const permissions = [
    'ADMINISTRATOR',
    'MANAGE_GUILD',
    'MANAGE_CHANNELS',
    'MANAGE_MESSAGES',
    'KICK_MEMBERS',
    'BAN_MEMBERS',
    'MODERATE_MEMBERS'
  ]

  return (
    <div className="commands-panel">
      <div className="panel-header">
        <h2>⚡ إدارة الأوامر الإدارية</h2>
        <button 
          className="btn btn-success"
          onClick={() => setShowAddForm(!showAddForm)}
        >
          {showAddForm ? 'إلغاء' : '+ إضافة أمر جديد'}
        </button>
      </div>

      {showAddForm && (
        <div className="panel add-command-form">
          <h3>إضافة أمر جديد</h3>
          <div className="form-grid">
            <div className="input-group">
              <label>اسم الأمر</label>
              <input
                type="text"
                value={newCommand.name}
                onChange={(e) => setNewCommand({...newCommand, name: e.target.value})}
                placeholder="مثال: ban"
              />
            </div>
            <div className="input-group">
              <label>وصف الأمر</label>
              <input
                type="text"
                value={newCommand.description}
                onChange={(e) => setNewCommand({...newCommand, description: e.target.value})}
                placeholder="وصف مختصر للأمر"
              />
            </div>
            <div className="input-group">
              <label>الصلاحية المطلوبة</label>
              <select
                value={newCommand.permission}
                onChange={(e) => setNewCommand({...newCommand, permission: e.target.value})}
              >
                {permissions.map(perm => (
                  <option key={perm} value={perm}>{perm}</option>
                ))}
              </select>
            </div>
          </div>
          <div className="form-actions">
            <button className="btn btn-success" onClick={addCommand}>
              إضافة الأمر
            </button>
            <button className="btn" onClick={() => setShowAddForm(false)}>
              إلغاء
            </button>
          </div>
        </div>
      )}

      <div className="commands-grid">
        {commands.map(command => (
          <div key={command.id} className={`panel command-card ${!command.enabled ? 'disabled' : ''}`}>
            <div className="command-header">
              <h3>/{command.name}</h3>
              <div className="command-actions">
                <button
                  className={`btn btn-sm ${command.enabled ? 'btn-warning' : 'btn-success'}`}
                  onClick={() => toggleCommand(command.id)}
                >
                  {command.enabled ? 'تعطيل' : 'تفعيل'}
                </button>
                <button
                  className="btn btn-sm btn-danger"
                  onClick={() => deleteCommand(command.id)}
                >
                  حذف
                </button>
              </div>
            </div>
            <p className="command-description">{command.description}</p>
            <div className="command-details">
              <span className="permission-badge">{command.permission}</span>
              <span className={`status ${command.enabled ? 'status-online' : 'status-offline'}`}>
                {command.enabled ? 'نشط' : 'معطل'}
              </span>
            </div>
          </div>
        ))}
      </div>

      <div className="panel">
        <h3>إعدادات عامة للأوامر</h3>
        <div className="settings-grid">
          <div className="setting-item">
            <label>بادئة الأوامر</label>
            <input type="text" defaultValue="!" placeholder="!" />
          </div>
          <div className="setting-item">
            <label>تأخير بين الأوامر (ثانية)</label>
            <input type="number" defaultValue="3" min="0" max="60" />
          </div>
          <div className="setting-item">
            <label>تسجيل استخدام الأوامر</label>
            <input type="checkbox" defaultChecked />
          </div>
        </div>
        <button className="btn btn-success">حفظ الإعدادات</button>
      </div>
    </div>
  )
}

export default CommandsPanel
