/* Discord Color Scheme */
:root {
  --discord-dark: #2c2f33;
  --discord-darker: #23272a;
  --discord-light: #99aab5;
  --discord-white: #ffffff;
  --discord-blurple: #5865f2;
  --discord-green: #57f287;
  --discord-red: #ed4245;
  --discord-yellow: #fee75c;
  --discord-orange: #ff9500;
  --discord-purple: #9c59b6;
  --discord-pink: #eb459e;
  --discord-cyan: #00d4aa;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, var(--discord-darker) 0%, var(--discord-dark) 100%);
  color: var(--discord-white);
  min-height: 100vh;
  overflow-x: hidden;
}

#root {
  min-height: 100vh;
  width: 100%;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--discord-blurple);
  }
  50% {
    box-shadow: 0 0 20px var(--discord-blurple), 0 0 30px var(--discord-blurple);
  }
}

/* Main App Container */
.app {
  min-height: 100vh;
  background: var(--discord-darker);
  animation: fadeIn 0.8s ease-out;
}

/* Header */
.header {
  background: var(--discord-dark);
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  position: sticky;
  top: 0;
  z-index: 100;
  animation: slideIn 0.6s ease-out;
}

.header h1 {
  color: var(--discord-blurple);
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
  text-shadow: 0 0 10px rgba(88, 101, 242, 0.5);
}

/* Dashboard Container */
.dashboard {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  animation: fadeIn 1s ease-out 0.3s both;
}

/* Panel Grid */
.panels-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

/* Panel Base Styles */
.panel {
  background: var(--discord-dark);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(88, 101, 242, 0.2);
  transition: all 0.3s ease;
  animation: fadeIn 0.8s ease-out;
  position: relative;
  overflow: hidden;
}

.panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--discord-blurple), var(--discord-cyan));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.panel:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  border-color: var(--discord-blurple);
}

.panel:hover::before {
  opacity: 1;
}

.panel h2 {
  color: var(--discord-blurple);
  font-size: 1.5rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.panel-icon {
  font-size: 1.8rem;
  animation: pulse 2s infinite;
}

/* Button Styles */
.btn {
  background: var(--discord-blurple);
  color: var(--discord-white);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0.5rem 0.5rem 0.5rem 0;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.btn:hover::before {
  width: 300px;
  height: 300px;
}

.btn:hover {
  background: #4752c4;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(88, 101, 242, 0.4);
}

.btn:active {
  transform: translateY(0);
}

/* Success Button */
.btn-success {
  background: var(--discord-green);
}

.btn-success:hover {
  background: #4ac776;
}

/* Danger Button */
.btn-danger {
  background: var(--discord-red);
}

.btn-danger:hover {
  background: #d63031;
}

/* Warning Button */
.btn-warning {
  background: var(--discord-yellow);
  color: var(--discord-darker);
}

.btn-warning:hover {
  background: #fdcb6e;
}

/* Input Styles */
.input-group {
  margin: 1rem 0;
}

.input-group label {
  display: block;
  color: var(--discord-light);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.input-group input,
.input-group select,
.input-group textarea {
  width: 100%;
  padding: 0.75rem;
  background: var(--discord-darker);
  border: 1px solid rgba(88, 101, 242, 0.3);
  border-radius: 6px;
  color: var(--discord-white);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.input-group input:focus,
.input-group select:focus,
.input-group textarea:focus {
  outline: none;
  border-color: var(--discord-blurple);
  box-shadow: 0 0 0 2px rgba(88, 101, 242, 0.2);
}

/* Status Indicators */
.status {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-online {
  background: var(--discord-green);
  color: var(--discord-white);
}

.status-offline {
  background: var(--discord-red);
  color: var(--discord-white);
}

.status-idle {
  background: var(--discord-yellow);
  color: var(--discord-darker);
}

/* Dashboard Navigation */
.dashboard-nav {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--discord-dark);
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  overflow-x: auto;
}

.nav-btn {
  background: transparent;
  color: var(--discord-light);
  border: 1px solid rgba(88, 101, 242, 0.3);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.nav-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(88, 101, 242, 0.2), transparent);
  transition: left 0.5s ease;
}

.nav-btn:hover::before {
  left: 100%;
}

.nav-btn:hover {
  color: var(--discord-white);
  border-color: var(--discord-blurple);
  transform: translateY(-2px);
}

.nav-btn.active {
  background: var(--discord-blurple);
  color: var(--discord-white);
  border-color: var(--discord-blurple);
  box-shadow: 0 4px 15px rgba(88, 101, 242, 0.4);
}

/* Panel Header */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--discord-dark);
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.panel-header h2 {
  margin: 0;
  color: var(--discord-blurple);
  font-size: 1.8rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

/* Overview Panel Stats */
.panel-stats {
  display: flex;
  gap: 1rem;
  margin: 1rem 0;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem;
  background: rgba(88, 101, 242, 0.1);
  border-radius: 8px;
  min-width: 80px;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--discord-blurple);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--discord-light);
  text-align: center;
}

/* Commands Panel Styles */
.commands-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.command-card {
  transition: all 0.3s ease;
}

.command-card.disabled {
  opacity: 0.6;
  filter: grayscale(50%);
}

.command-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.command-header h3 {
  color: var(--discord-blurple);
  font-family: 'Courier New', monospace;
  background: rgba(88, 101, 242, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.command-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.command-description {
  color: var(--discord-light);
  margin-bottom: 1rem;
  line-height: 1.5;
}

.command-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.permission-badge {
  background: rgba(88, 101, 242, 0.2);
  color: var(--discord-blurple);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

/* Form Styles */
.add-command-form,
.add-user-form,
.add-role-form {
  animation: slideIn 0.3s ease-out;
  margin-bottom: 2rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Logs Panel Styles */
.logs-controls {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.logs-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--discord-dark);
  padding: 1.5rem;
  border-radius: 12px;
  text-align: center;
  border: 1px solid rgba(88, 101, 242, 0.2);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  border-color: var(--discord-blurple);
}

.logs-container {
  max-height: 600px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.logs-container::-webkit-scrollbar {
  width: 8px;
}

.logs-container::-webkit-scrollbar-track {
  background: var(--discord-darker);
  border-radius: 4px;
}

.logs-container::-webkit-scrollbar-thumb {
  background: var(--discord-blurple);
  border-radius: 4px;
}

.log-entry {
  margin-bottom: 1rem;
  animation: fadeIn 0.5s ease-out;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.log-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.log-icon {
  font-size: 1.2rem;
}

.log-timestamp {
  color: var(--discord-light);
  font-size: 0.875rem;
  font-family: 'Courier New', monospace;
}

.log-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
}

.log-detail {
  color: var(--discord-light);
  font-size: 0.875rem;
}

.log-detail strong {
  color: var(--discord-white);
}

.no-logs {
  text-align: center;
  padding: 3rem;
  color: var(--discord-light);
}

/* Protection Panel Styles */
.protection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.protection-card {
  border-left: 4px solid var(--discord-blurple);
}

.protection-header {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.protection-icon {
  font-size: 2rem;
  min-width: 50px;
  text-align: center;
}

.protection-info {
  flex: 1;
}

.protection-info h3 {
  margin: 0 0 0.5rem 0;
  color: var(--discord-white);
}

.protection-info p {
  margin: 0;
  color: var(--discord-light);
  font-size: 0.875rem;
}

/* Toggle Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--discord-green);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.protection-status {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.spam-settings {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.word-filter-section {
  margin-top: 1rem;
}

.add-word-form .input-group {
  display: flex;
  gap: 0.5rem;
  align-items: end;
}

.add-word-form input {
  flex: 1;
}

.banned-words-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
}

.banned-word-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(237, 66, 69, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid var(--discord-red);
}

.protection-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.panel-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  padding: 2rem;
  background: var(--discord-dark);
  border-radius: 12px;
}

/* Access Panel Styles */
.access-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 2rem 0;
}

.users-list,
.roles-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.user-card,
.role-card {
  background: var(--discord-darker);
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid rgba(88, 101, 242, 0.2);
  transition: all 0.3s ease;
}

.user-card:hover,
.role-card:hover {
  border-color: var(--discord-blurple);
  transform: translateY(-2px);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.user-avatar {
  position: relative;
  width: 50px;
  height: 50px;
  background: var(--discord-blurple);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
}

.status-dot {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid var(--discord-dark);
}

.status-dot.online {
  background: var(--discord-green);
}

.status-dot.offline {
  background: var(--discord-red);
}

.status-dot.idle {
  background: var(--discord-yellow);
}

.user-details h4 {
  margin: 0;
  color: var(--discord-white);
}

.user-role {
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.875rem;
}

.user-last-seen {
  color: var(--discord-light);
  font-size: 0.75rem;
}

.user-permissions,
.role-permissions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 1rem 0;
}

.permission-badge {
  background: rgba(88, 101, 242, 0.2);
  color: var(--discord-blurple);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.permission-badge.all {
  background: rgba(87, 242, 135, 0.2);
  color: var(--discord-green);
}

.user-actions,
.role-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.role-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.role-info h4 {
  margin: 0;
  font-size: 1.2rem;
}

.role-users {
  color: var(--discord-light);
  font-size: 0.875rem;
}

.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
  margin: 1rem 0;
}

.permission-item {
  display: flex;
  align-items: center;
}

.permission-item label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: var(--discord-light);
  font-size: 0.875rem;
}

.permission-item input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.permission-icon {
  font-size: 1rem;
}

.security-settings {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.security-settings .setting-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--discord-darker);
  border-radius: 8px;
}

.security-settings .setting-item input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.security-settings .setting-item input[type="number"] {
  width: 80px;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .access-sections {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 1rem;
  }

  .panels-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .header {
    padding: 1rem;
  }

  .header h1 {
    font-size: 1.5rem;
  }

  .dashboard-nav {
    flex-direction: column;
    gap: 0.5rem;
  }

  .panel-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .header-actions {
    justify-content: center;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .logs-controls {
    grid-template-columns: 1fr;
  }

  .protection-grid {
    grid-template-columns: 1fr;
  }

  .panel-actions {
    flex-direction: column;
  }
}
