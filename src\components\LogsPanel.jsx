import { useState } from 'react'

function LogsPanel() {
  const [logs, setLogs] = useState([
    { id: 1, type: 'ban', user: 'User#1234', moderator: 'Admin#5678', reason: 'مخالفة القوانين', timestamp: '2024-01-15 14:30:25', channel: 'general' },
    { id: 2, type: 'kick', user: 'BadUser#9999', moderator: 'Mod#1111', reason: 'سلوك غير لائق', timestamp: '2024-01-15 14:25:10', channel: 'general' },
    { id: 3, type: 'mute', user: 'Spammer#2222', moderator: 'Admin#5678', reason: 'إرسال رسائل مزعجة', timestamp: '2024-01-15 14:20:45', channel: 'chat' },
    { id: 4, type: 'warn', user: 'NewUser#3333', moderator: 'Mod#4444', reason: 'تحذير أول', timestamp: '2024-01-15 14:15:30', channel: 'general' },
    { id: 5, type: 'clear', user: 'System', moderator: 'Admin#5678', reason: 'تنظيف القناة', timestamp: '2024-01-15 14:10:15', channel: 'announcements' },
  ])

  const [filter, setFilter] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')

  const logTypes = {
    ban: { icon: '🔨', color: 'var(--discord-red)', label: 'حظر' },
    kick: { icon: '👢', color: 'var(--discord-orange)', label: 'طرد' },
    mute: { icon: '🔇', color: 'var(--discord-yellow)', label: 'كتم' },
    warn: { icon: '⚠️', color: 'var(--discord-yellow)', label: 'تحذير' },
    clear: { icon: '🧹', color: 'var(--discord-cyan)', label: 'حذف رسائل' },
    join: { icon: '📥', color: 'var(--discord-green)', label: 'انضمام' },
    leave: { icon: '📤', color: 'var(--discord-red)', label: 'مغادرة' }
  }

  const filteredLogs = logs.filter(log => {
    const matchesFilter = filter === 'all' || log.type === filter
    const matchesSearch = log.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.moderator.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.reason.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesFilter && matchesSearch
  })

  const clearLogs = () => {
    if (confirm('هل أنت متأكد من حذف جميع السجلات؟')) {
      setLogs([])
    }
  }

  const exportLogs = () => {
    const dataStr = JSON.stringify(logs, null, 2)
    const dataBlob = new Blob([dataStr], {type: 'application/json'})
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `logs_${new Date().toISOString().split('T')[0]}.json`
    link.click()
  }

  return (
    <div className="logs-panel">
      <div className="panel-header">
        <h2>📋 إدارة السجلات</h2>
        <div className="header-actions">
          <button className="btn btn-success" onClick={exportLogs}>
            📥 تصدير السجلات
          </button>
          <button className="btn btn-danger" onClick={clearLogs}>
            🗑️ حذف جميع السجلات
          </button>
        </div>
      </div>

      <div className="panel">
        <div className="logs-controls">
          <div className="input-group">
            <label>البحث في السجلات</label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="ابحث عن مستخدم، مشرف، أو سبب..."
            />
          </div>
          <div className="input-group">
            <label>تصفية حسب النوع</label>
            <select value={filter} onChange={(e) => setFilter(e.target.value)}>
              <option value="all">جميع السجلات</option>
              <option value="ban">حظر</option>
              <option value="kick">طرد</option>
              <option value="mute">كتم</option>
              <option value="warn">تحذير</option>
              <option value="clear">حذف رسائل</option>
            </select>
          </div>
        </div>
      </div>

      <div className="logs-stats">
        <div className="stat-card">
          <span className="stat-number">{logs.length}</span>
          <span className="stat-label">إجمالي السجلات</span>
        </div>
        <div className="stat-card">
          <span className="stat-number">{logs.filter(l => l.type === 'ban').length}</span>
          <span className="stat-label">حالات حظر</span>
        </div>
        <div className="stat-card">
          <span className="stat-number">{logs.filter(l => l.type === 'kick').length}</span>
          <span className="stat-label">حالات طرد</span>
        </div>
        <div className="stat-card">
          <span className="stat-number">{logs.filter(l => l.type === 'warn').length}</span>
          <span className="stat-label">تحذيرات</span>
        </div>
      </div>

      <div className="logs-container">
        {filteredLogs.length === 0 ? (
          <div className="panel no-logs">
            <p>لا توجد سجلات تطابق البحث</p>
          </div>
        ) : (
          filteredLogs.map(log => (
            <div key={log.id} className="panel log-entry">
              <div className="log-header">
                <div className="log-type" style={{ color: logTypes[log.type]?.color }}>
                  <span className="log-icon">{logTypes[log.type]?.icon}</span>
                  <span className="log-label">{logTypes[log.type]?.label}</span>
                </div>
                <div className="log-timestamp">{log.timestamp}</div>
              </div>
              <div className="log-content">
                <div className="log-detail">
                  <strong>المستخدم:</strong> {log.user}
                </div>
                <div className="log-detail">
                  <strong>المشرف:</strong> {log.moderator}
                </div>
                <div className="log-detail">
                  <strong>القناة:</strong> #{log.channel}
                </div>
                <div className="log-detail">
                  <strong>السبب:</strong> {log.reason}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      <div className="panel">
        <h3>إعدادات السجلات</h3>
        <div className="settings-grid">
          <div className="setting-item">
            <label>قناة السجلات</label>
            <select defaultValue="logs">
              <option value="logs">#logs</option>
              <option value="mod-logs">#mod-logs</option>
              <option value="admin-logs">#admin-logs</option>
            </select>
          </div>
          <div className="setting-item">
            <label>الاحتفاظ بالسجلات (أيام)</label>
            <input type="number" defaultValue="30" min="1" max="365" />
          </div>
          <div className="setting-item">
            <label>تسجيل تلقائي</label>
            <input type="checkbox" defaultChecked />
          </div>
        </div>
        <button className="btn btn-success">حفظ إعدادات السجلات</button>
      </div>
    </div>
  )
}

export default LogsPanel
