import { useState } from 'react'

function ProtectionPanel() {
  const [protectionSettings, setProtectionSettings] = useState({
    antiSpam: true,
    antiRaid: true,
    antiBot: false,
    autoMod: true,
    linkFilter: true,
    wordFilter: true,
    capsFilter: false,
    mentionSpam: true
  })

  const [spamSettings, setSpamSettings] = useState({
    maxMessages: 5,
    timeWindow: 10,
    punishment: 'mute',
    duration: 300
  })

  const [bannedWords, setBannedWords] = useState([
    'كلمة محظورة 1',
    'كلمة محظورة 2',
    'كلمة محظورة 3'
  ])

  const [newWord, setNewWord] = useState('')

  const toggleProtection = (setting) => {
    setProtectionSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }))
  }

  const addBannedWord = () => {
    if (newWord.trim() && !bannedWords.includes(newWord.trim())) {
      setBannedWords([...bannedWords, newWord.trim()])
      setNewWord('')
    }
  }

  const removeBannedWord = (word) => {
    setBannedWords(bannedWords.filter(w => w !== word))
  }

  const protectionFeatures = [
    {
      key: 'antiSpam',
      title: 'مكافحة الرسائل المزعجة',
      description: 'منع إرسال رسائل متكررة بسرعة',
      icon: '🚫',
      color: 'var(--discord-red)'
    },
    {
      key: 'antiRaid',
      title: 'مكافحة الغارات',
      description: 'حماية من انضمام عدد كبير من الحسابات',
      icon: '🛡️',
      color: 'var(--discord-blurple)'
    },
    {
      key: 'antiBot',
      title: 'مكافحة البوتات',
      description: 'منع البوتات غير المرغوب فيها',
      icon: '🤖',
      color: 'var(--discord-orange)'
    },
    {
      key: 'autoMod',
      title: 'الإشراف التلقائي',
      description: 'إجراءات تلقائية ضد المخالفات',
      icon: '⚖️',
      color: 'var(--discord-green)'
    },
    {
      key: 'linkFilter',
      title: 'تصفية الروابط',
      description: 'منع الروابط المشبوهة والضارة',
      icon: '🔗',
      color: 'var(--discord-yellow)'
    },
    {
      key: 'wordFilter',
      title: 'تصفية الكلمات',
      description: 'حذف الكلمات المحظورة تلقائياً',
      icon: '🔤',
      color: 'var(--discord-purple)'
    },
    {
      key: 'capsFilter',
      title: 'تصفية الأحرف الكبيرة',
      description: 'منع الرسائل بأحرف كبيرة فقط',
      icon: '🔠',
      color: 'var(--discord-cyan)'
    },
    {
      key: 'mentionSpam',
      title: 'مكافحة إزعاج المنشن',
      description: 'منع الإفراط في استخدام المنشن',
      icon: '@',
      color: 'var(--discord-pink)'
    }
  ]

  return (
    <div className="protection-panel">
      <div className="panel-header">
        <h2>🛡️ إعدادات الحماية</h2>
        <div className="protection-status">
          <span className="status status-online">نظام الحماية نشط</span>
        </div>
      </div>

      <div className="protection-grid">
        {protectionFeatures.map(feature => (
          <div key={feature.key} className="panel protection-card">
            <div className="protection-header">
              <div className="protection-icon" style={{ color: feature.color }}>
                {feature.icon}
              </div>
              <div className="protection-info">
                <h3>{feature.title}</h3>
                <p>{feature.description}</p>
              </div>
              <div className="protection-toggle">
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={protectionSettings[feature.key]}
                    onChange={() => toggleProtection(feature.key)}
                  />
                  <span className="slider"></span>
                </label>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="panel">
        <h3>⚙️ إعدادات مكافحة الرسائل المزعجة</h3>
        <div className="spam-settings">
          <div className="input-group">
            <label>الحد الأقصى للرسائل</label>
            <input
              type="number"
              value={spamSettings.maxMessages}
              onChange={(e) => setSpamSettings({...spamSettings, maxMessages: parseInt(e.target.value)})}
              min="2"
              max="20"
            />
          </div>
          <div className="input-group">
            <label>النافذة الزمنية (ثانية)</label>
            <input
              type="number"
              value={spamSettings.timeWindow}
              onChange={(e) => setSpamSettings({...spamSettings, timeWindow: parseInt(e.target.value)})}
              min="5"
              max="60"
            />
          </div>
          <div className="input-group">
            <label>نوع العقوبة</label>
            <select
              value={spamSettings.punishment}
              onChange={(e) => setSpamSettings({...spamSettings, punishment: e.target.value})}
            >
              <option value="warn">تحذير</option>
              <option value="mute">كتم</option>
              <option value="kick">طرد</option>
              <option value="ban">حظر</option>
            </select>
          </div>
          <div className="input-group">
            <label>مدة العقوبة (ثانية)</label>
            <input
              type="number"
              value={spamSettings.duration}
              onChange={(e) => setSpamSettings({...spamSettings, duration: parseInt(e.target.value)})}
              min="60"
              max="86400"
            />
          </div>
        </div>
      </div>

      <div className="panel">
        <h3>🔤 إدارة الكلمات المحظورة</h3>
        <div className="word-filter-section">
          <div className="add-word-form">
            <div className="input-group">
              <input
                type="text"
                value={newWord}
                onChange={(e) => setNewWord(e.target.value)}
                placeholder="أضف كلمة محظورة جديدة..."
                onKeyPress={(e) => e.key === 'Enter' && addBannedWord()}
              />
              <button className="btn btn-success" onClick={addBannedWord}>
                إضافة
              </button>
            </div>
          </div>
          <div className="banned-words-list">
            {bannedWords.map((word, index) => (
              <div key={index} className="banned-word-item">
                <span>{word}</span>
                <button
                  className="btn btn-sm btn-danger"
                  onClick={() => removeBannedWord(word)}
                >
                  حذف
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="panel">
        <h3>📊 إحصائيات الحماية</h3>
        <div className="protection-stats">
          <div className="stat-card">
            <span className="stat-number">127</span>
            <span className="stat-label">رسائل محذوفة اليوم</span>
          </div>
          <div className="stat-card">
            <span className="stat-number">8</span>
            <span className="stat-label">مستخدمين محظورين</span>
          </div>
          <div className="stat-card">
            <span className="stat-number">23</span>
            <span className="stat-label">روابط محجوبة</span>
          </div>
          <div className="stat-card">
            <span className="stat-number">45</span>
            <span className="stat-label">كلمات مصفاة</span>
          </div>
        </div>
      </div>

      <div className="panel-actions">
        <button className="btn btn-success">حفظ جميع الإعدادات</button>
        <button className="btn btn-warning">إعادة تعيين للافتراضي</button>
        <button className="btn btn-danger">تعطيل جميع الحماية</button>
      </div>
    </div>
  )
}

export default ProtectionPanel
