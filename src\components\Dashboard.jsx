import { useState } from 'react'
import CommandsPanel from './CommandsPanel'
import LogsPanel from './LogsPanel'
import ProtectionPanel from './ProtectionPanel'
import AccessPanel from './AccessPanel'

function Dashboard() {
  const [activePanel, setActivePanel] = useState('overview')

  const renderActivePanel = () => {
    switch (activePanel) {
      case 'commands':
        return <CommandsPanel />
      case 'logs':
        return <LogsPanel />
      case 'protection':
        return <ProtectionPanel />
      case 'access':
        return <AccessPanel />
      default:
        return (
          <div className="overview-panel">
            <div className="panels-grid">
              <div className="panel" onClick={() => setActivePanel('commands')}>
                <h2>
                  <span className="panel-icon">⚡</span>
                  الأوامر الإدارية
                </h2>
                <p>إدارة وتعديل أوامر البوت والصلاحيات</p>
                <div className="panel-stats">
                  <div className="stat">
                    <span className="stat-number">24</span>
                    <span className="stat-label">أمر نشط</span>
                  </div>
                  <div className="stat">
                    <span className="stat-number">12</span>
                    <span className="stat-label">أمر مخصص</span>
                  </div>
                </div>
                <button className="btn btn-primary">إدارة الأوامر</button>
              </div>

              <div className="panel" onClick={() => setActivePanel('logs')}>
                <h2>
                  <span className="panel-icon">📋</span>
                  السجلات
                </h2>
                <p>عرض وإدارة سجلات الخادم والأنشطة</p>
                <div className="panel-stats">
                  <div className="stat">
                    <span className="stat-number">1,247</span>
                    <span className="stat-label">سجل اليوم</span>
                  </div>
                  <div className="stat">
                    <span className="stat-number status-online">متصل</span>
                    <span className="stat-label">حالة التسجيل</span>
                  </div>
                </div>
                <button className="btn btn-success">عرض السجلات</button>
              </div>

              <div className="panel" onClick={() => setActivePanel('protection')}>
                <h2>
                  <span className="panel-icon">🛡️</span>
                  الحماية
                </h2>
                <p>إعدادات الحماية والأمان للخادم</p>
                <div className="panel-stats">
                  <div className="stat">
                    <span className="stat-number">8</span>
                    <span className="stat-label">تهديد محجوب</span>
                  </div>
                  <div className="stat">
                    <span className="stat-number status-online">نشط</span>
                    <span className="stat-label">حالة الحماية</span>
                  </div>
                </div>
                <button className="btn btn-warning">إعدادات الحماية</button>
              </div>

              <div className="panel" onClick={() => setActivePanel('access')}>
                <h2>
                  <span className="panel-icon">🔐</span>
                  التحكم بالوصول
                </h2>
                <p>إدارة صلاحيات البوت والموقع</p>
                <div className="panel-stats">
                  <div className="stat">
                    <span className="stat-number">15</span>
                    <span className="stat-label">مستخدم مخول</span>
                  </div>
                  <div className="stat">
                    <span className="stat-number">3</span>
                    <span className="stat-label">دور إداري</span>
                  </div>
                </div>
                <button className="btn btn-danger">إدارة الوصول</button>
              </div>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="dashboard">
      <nav className="dashboard-nav">
        <button 
          className={`nav-btn ${activePanel === 'overview' ? 'active' : ''}`}
          onClick={() => setActivePanel('overview')}
        >
          🏠 الرئيسية
        </button>
        <button 
          className={`nav-btn ${activePanel === 'commands' ? 'active' : ''}`}
          onClick={() => setActivePanel('commands')}
        >
          ⚡ الأوامر
        </button>
        <button 
          className={`nav-btn ${activePanel === 'logs' ? 'active' : ''}`}
          onClick={() => setActivePanel('logs')}
        >
          📋 السجلات
        </button>
        <button 
          className={`nav-btn ${activePanel === 'protection' ? 'active' : ''}`}
          onClick={() => setActivePanel('protection')}
        >
          🛡️ الحماية
        </button>
        <button 
          className={`nav-btn ${activePanel === 'access' ? 'active' : ''}`}
          onClick={() => setActivePanel('access')}
        >
          🔐 الوصول
        </button>
      </nav>

      <div className="dashboard-content">
        {renderActivePanel()}
      </div>
    </div>
  )
}

export default Dashboard
