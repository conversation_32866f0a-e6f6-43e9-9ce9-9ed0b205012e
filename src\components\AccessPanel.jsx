import { useState } from 'react'

function AccessPanel() {
  const [users, setUsers] = useState([
    { id: 1, username: 'Admin#1234', role: 'owner', permissions: ['all'], status: 'online', lastSeen: 'الآن' },
    { id: 2, username: 'Moderator#5678', role: 'admin', permissions: ['ban', 'kick', 'mute'], status: 'online', lastSeen: 'منذ 5 دقائق' },
    { id: 3, username: 'Helper#9999', role: 'moderator', permissions: ['mute', 'warn'], status: 'offline', lastSeen: 'منذ ساعة' },
  ])

  const [roles, setRoles] = useState([
    { id: 1, name: 'Owner', permissions: ['all'], color: '#ff0000', users: 1 },
    { id: 2, name: 'Admin', permissions: ['ban', 'kick', 'mute', 'manage'], color: '#ff6600', users: 1 },
    { id: 3, name: 'Moderator', permissions: ['mute', 'warn', 'clear'], color: '#00ff00', users: 1 },
    { id: 4, name: 'Helper', permissions: ['warn'], color: '#0066ff', users: 0 },
  ])

  const [newUser, setNewUser] = useState({ username: '', role: 'helper' })
  const [newRole, setNewRole] = useState({ name: '', permissions: [], color: '#5865f2' })
  const [showAddUser, setShowAddUser] = useState(false)
  const [showAddRole, setShowAddRole] = useState(false)

  const availablePermissions = [
    { key: 'ban', label: 'حظر الأعضاء', icon: '🔨' },
    { key: 'kick', label: 'طرد الأعضاء', icon: '👢' },
    { key: 'mute', label: 'كتم الأعضاء', icon: '🔇' },
    { key: 'warn', label: 'تحذير الأعضاء', icon: '⚠️' },
    { key: 'clear', label: 'حذف الرسائل', icon: '🧹' },
    { key: 'manage', label: 'إدارة الخادم', icon: '⚙️' },
    { key: 'logs', label: 'عرض السجلات', icon: '📋' },
    { key: 'settings', label: 'تعديل الإعدادات', icon: '🔧' }
  ]

  const addUser = () => {
    if (newUser.username) {
      const user = {
        id: Date.now(),
        username: newUser.username,
        role: newUser.role,
        permissions: getRolePermissions(newUser.role),
        status: 'offline',
        lastSeen: 'لم يتصل بعد'
      }
      setUsers([...users, user])
      setNewUser({ username: '', role: 'helper' })
      setShowAddUser(false)
    }
  }

  const removeUser = (id) => {
    setUsers(users.filter(user => user.id !== id))
  }

  const addRole = () => {
    if (newRole.name && newRole.permissions.length > 0) {
      const role = {
        id: Date.now(),
        name: newRole.name,
        permissions: newRole.permissions,
        color: newRole.color,
        users: 0
      }
      setRoles([...roles, role])
      setNewRole({ name: '', permissions: [], color: '#5865f2' })
      setShowAddRole(false)
    }
  }

  const removeRole = (id) => {
    setRoles(roles.filter(role => role.id !== id))
  }

  const getRolePermissions = (roleName) => {
    const role = roles.find(r => r.name.toLowerCase() === roleName.toLowerCase())
    return role ? role.permissions : []
  }

  const togglePermission = (permission) => {
    setNewRole(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...prev.permissions, permission]
    }))
  }

  return (
    <div className="access-panel">
      <div className="panel-header">
        <h2>🔐 إدارة التحكم بالوصول</h2>
        <div className="header-actions">
          <button 
            className="btn btn-success"
            onClick={() => setShowAddUser(!showAddUser)}
          >
            + إضافة مستخدم
          </button>
          <button 
            className="btn btn-warning"
            onClick={() => setShowAddRole(!showAddRole)}
          >
            + إضافة دور
          </button>
        </div>
      </div>

      {showAddUser && (
        <div className="panel add-user-form">
          <h3>إضافة مستخدم جديد</h3>
          <div className="form-grid">
            <div className="input-group">
              <label>اسم المستخدم</label>
              <input
                type="text"
                value={newUser.username}
                onChange={(e) => setNewUser({...newUser, username: e.target.value})}
                placeholder="Username#1234"
              />
            </div>
            <div className="input-group">
              <label>الدور</label>
              <select
                value={newUser.role}
                onChange={(e) => setNewUser({...newUser, role: e.target.value})}
              >
                {roles.map(role => (
                  <option key={role.id} value={role.name.toLowerCase()}>
                    {role.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <div className="form-actions">
            <button className="btn btn-success" onClick={addUser}>
              إضافة المستخدم
            </button>
            <button className="btn" onClick={() => setShowAddUser(false)}>
              إلغاء
            </button>
          </div>
        </div>
      )}

      {showAddRole && (
        <div className="panel add-role-form">
          <h3>إضافة دور جديد</h3>
          <div className="form-grid">
            <div className="input-group">
              <label>اسم الدور</label>
              <input
                type="text"
                value={newRole.name}
                onChange={(e) => setNewRole({...newRole, name: e.target.value})}
                placeholder="اسم الدور"
              />
            </div>
            <div className="input-group">
              <label>لون الدور</label>
              <input
                type="color"
                value={newRole.color}
                onChange={(e) => setNewRole({...newRole, color: e.target.value})}
              />
            </div>
          </div>
          <div className="permissions-grid">
            <label>الصلاحيات:</label>
            {availablePermissions.map(perm => (
              <div key={perm.key} className="permission-item">
                <label>
                  <input
                    type="checkbox"
                    checked={newRole.permissions.includes(perm.key)}
                    onChange={() => togglePermission(perm.key)}
                  />
                  <span className="permission-icon">{perm.icon}</span>
                  {perm.label}
                </label>
              </div>
            ))}
          </div>
          <div className="form-actions">
            <button className="btn btn-success" onClick={addRole}>
              إضافة الدور
            </button>
            <button className="btn" onClick={() => setShowAddRole(false)}>
              إلغاء
            </button>
          </div>
        </div>
      )}

      <div className="access-sections">
        <div className="panel">
          <h3>👥 المستخدمون المخولون</h3>
          <div className="users-list">
            {users.map(user => (
              <div key={user.id} className="user-card">
                <div className="user-info">
                  <div className="user-avatar">
                    <span className={`status-dot ${user.status}`}></span>
                  </div>
                  <div className="user-details">
                    <h4>{user.username}</h4>
                    <span className="user-role" style={{ color: roles.find(r => r.name.toLowerCase() === user.role)?.color }}>
                      {user.role}
                    </span>
                    <span className="user-last-seen">آخر ظهور: {user.lastSeen}</span>
                  </div>
                </div>
                <div className="user-permissions">
                  {user.permissions.includes('all') ? (
                    <span className="permission-badge all">جميع الصلاحيات</span>
                  ) : (
                    user.permissions.map(perm => (
                      <span key={perm} className="permission-badge">
                        {availablePermissions.find(p => p.key === perm)?.icon} {perm}
                      </span>
                    ))
                  )}
                </div>
                <div className="user-actions">
                  <button className="btn btn-sm btn-warning">تعديل</button>
                  <button 
                    className="btn btn-sm btn-danger"
                    onClick={() => removeUser(user.id)}
                  >
                    حذف
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="panel">
          <h3>🎭 الأدوار والصلاحيات</h3>
          <div className="roles-list">
            {roles.map(role => (
              <div key={role.id} className="role-card">
                <div className="role-header">
                  <div className="role-info">
                    <h4 style={{ color: role.color }}>{role.name}</h4>
                    <span className="role-users">{role.users} مستخدم</span>
                  </div>
                  <div className="role-actions">
                    <button className="btn btn-sm btn-warning">تعديل</button>
                    <button 
                      className="btn btn-sm btn-danger"
                      onClick={() => removeRole(role.id)}
                    >
                      حذف
                    </button>
                  </div>
                </div>
                <div className="role-permissions">
                  {role.permissions.includes('all') ? (
                    <span className="permission-badge all">جميع الصلاحيات</span>
                  ) : (
                    role.permissions.map(perm => (
                      <span key={perm} className="permission-badge">
                        {availablePermissions.find(p => p.key === perm)?.icon} 
                        {availablePermissions.find(p => p.key === perm)?.label}
                      </span>
                    ))
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="panel">
        <h3>🔒 إعدادات الأمان</h3>
        <div className="security-settings">
          <div className="setting-item">
            <label>المصادقة الثنائية مطلوبة</label>
            <input type="checkbox" defaultChecked />
          </div>
          <div className="setting-item">
            <label>تسجيل جميع العمليات</label>
            <input type="checkbox" defaultChecked />
          </div>
          <div className="setting-item">
            <label>انتهاء صلاحية الجلسة (دقيقة)</label>
            <input type="number" defaultValue="60" min="15" max="1440" />
          </div>
        </div>
        <button className="btn btn-success">حفظ إعدادات الأمان</button>
      </div>
    </div>
  )
}

export default AccessPanel
